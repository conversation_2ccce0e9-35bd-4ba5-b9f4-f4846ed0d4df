const { Pool } = require('pg')

async function setupTestUser() {
  const pool = new Pool({
    host: 'localhost',
    port: 5432,
    database: 'workwell',
    user: 'workwell_user',
    password: 'workwell_password',
  })

  try {
    console.log('Setting up test user for benefit rankings...')
    
    const client = await pool.connect()
    
    // First, create the user_benefit_rankings table if it doesn't exist
    console.log('Creating user_benefit_rankings table...')
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_benefit_rankings (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
        ranking INTEGER NOT NULL CHECK (ranking >= 1 AND ranking <= 10),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, benefit_id)
      );
    `)
    
    // Create indexes
    await client.query('CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_user_id ON user_benefit_rankings(user_id);')
    await client.query('CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_benefit_id ON user_benefit_rankings(benefit_id);')
    await client.query('CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_ranking ON user_benefit_rankings(ranking);')
    await client.query('CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_updated_at ON user_benefit_rankings(updated_at);')
    
    console.log('✓ Table and indexes created')
    
    // Find or create a test user
    let testUser = await client.query(`
      SELECT id, email, payment_status, role FROM users 
      WHERE email = '<EMAIL>' 
      LIMIT 1
    `)
    
    if (testUser.rows.length === 0) {
      console.log('Creating test user...')
      testUser = await client.query(`
        INSERT INTO users (email, first_name, last_name, role, payment_status)
        VALUES ('<EMAIL>', 'Test', 'User', 'admin', 'paying')
        RETURNING id, email, payment_status, role
      `)
      console.log('✓ Test user created')
    } else {
      // Update existing user to be paying and admin
      await client.query(`
        UPDATE users 
        SET payment_status = 'paying', role = 'admin' 
        WHERE id = $1
      `, [testUser.rows[0].id])
      console.log('✓ Test user updated to paying status')
    }
    
    const user = testUser.rows[0]
    console.log(`User: ${user.email} (${user.payment_status}, ${user.role})`)
    
    // Create a session for the test user
    const sessionToken = `test_session_${Date.now()}`
    await client.query(`
      INSERT INTO user_sessions (user_id, session_token, expires_at)
      VALUES ($1, $2, NOW() + INTERVAL '24 hours')
      ON CONFLICT (session_token) DO UPDATE SET expires_at = NOW() + INTERVAL '24 hours'
    `, [user.id, sessionToken])
    
    console.log(`✓ Session created: ${sessionToken}`)
    
    // Get some benefits to create test rankings
    const benefits = await client.query(`
      SELECT id, name FROM benefits LIMIT 10
    `)
    
    if (benefits.rows.length > 0) {
      console.log('Creating test benefit rankings...')
      
      // Delete existing rankings for this user
      await client.query('DELETE FROM user_benefit_rankings WHERE user_id = $1', [user.id])
      
      // Create some test rankings
      for (let i = 0; i < Math.min(5, benefits.rows.length); i++) {
        const benefit = benefits.rows[i]
        const ranking = i + 1
        
        await client.query(`
          INSERT INTO user_benefit_rankings (user_id, benefit_id, ranking)
          VALUES ($1, $2, $3)
        `, [user.id, benefit.id, ranking])
        
        console.log(`  ${ranking}. ${benefit.name}`)
      }
      
      console.log('✓ Test rankings created')
    }
    
    client.release()
    
    console.log('\n🎉 Setup complete!')
    console.log(`\nTo test:`)
    console.log(`1. Go to http://localhost:3001/sign-in`)
    console.log(`2. Sign in with: <EMAIL>`)
    console.log(`3. Go to Analytics > Benefit Rankings tab`)
    console.log(`4. You should see the test ranking data`)
    console.log(`\nSession token for API testing: ${sessionToken}`)
    
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await pool.end()
  }
}

setupTestUser()
