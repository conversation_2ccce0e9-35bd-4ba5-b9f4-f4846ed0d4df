const { Pool } = require('pg')

const pool = new Pool({
  connectionString: 'postgresql://workwell_user:workwell_password@localhost:5432/workwell',
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function createTable() {
  const client = await pool.connect()
  
  try {
    console.log('Creating user_benefit_rankings table...')
    
    // Create the table
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_benefit_rankings (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
        ranking INTEGER NOT NULL CHECK (ranking >= 1 AND ranking <= 10),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, benefit_id)
      );
    `)
    
    console.log('Creating indexes...')
    
    // Create indexes
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_user_id ON user_benefit_rankings(user_id);
    `)
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_benefit_id ON user_benefit_rankings(benefit_id);
    `)
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_ranking ON user_benefit_rankings(ranking);
    `)
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_updated_at ON user_benefit_rankings(updated_at);
    `)
    
    console.log('Table and indexes created successfully!')
    
    // Verify the table was created
    const result = await client.query("SELECT table_name FROM information_schema.tables WHERE table_name = 'user_benefit_rankings'")
    
    if (result.rows.length > 0) {
      console.log('✓ user_benefit_rankings table verified')
      
      // Show table structure
      const columns = await client.query(`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'user_benefit_rankings' 
        ORDER BY ordinal_position
      `)
      
      console.log('Table structure:')
      columns.rows.forEach(col => {
        console.log(`  ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : ''}`)
      })
    } else {
      console.log('✗ user_benefit_rankings table not found')
    }
    
  } catch (error) {
    console.error('Error:', error.message)
  } finally {
    client.release()
    await pool.end()
  }
}

createTable()
