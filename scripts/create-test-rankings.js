const { Pool } = require('pg')

async function createTestRankings() {
  const pool = new Pool({
    host: 'localhost',
    port: 5432,
    database: 'workwell',
    user: 'workwell_user',
    password: 'workwell_password',
  })

  try {
    const client = await pool.connect()
    console.log('Creating test benefit rankings...')
    
    // Get the admin user
    const adminUser = await client.query(`
      SELECT id, email FROM users WHERE email = '<EMAIL>'
    `)
    
    if (adminUser.rows.length === 0) {
      console.log('❌ Admin user not found')
      return
    }
    
    const userId = adminUser.rows[0].id
    console.log(`Using user: ${adminUser.rows[0].email}`)
    
    // Get some benefits
    const benefits = await client.query(`
      SELECT id, name FROM benefits ORDER BY name LIMIT 8
    `)
    
    console.log(`Found ${benefits.rows.length} benefits`)
    
    // Delete existing rankings for this user
    await client.query('DELETE FROM user_benefit_rankings WHERE user_id = $1', [userId])
    console.log('Cleared existing rankings')
    
    // Create test rankings
    for (let i = 0; i < benefits.rows.length; i++) {
      const benefit = benefits.rows[i]
      const ranking = i + 1
      
      await client.query(`
        INSERT INTO user_benefit_rankings (user_id, benefit_id, ranking, created_at, updated_at)
        VALUES ($1, $2, $3, NOW() - INTERVAL '${Math.floor(Math.random() * 30)} days', NOW())
      `, [userId, benefit.id, ranking])
      
      console.log(`  ${ranking}. ${benefit.name}`)
    }
    
    // Create rankings for a few more users to have more data
    const otherUsers = await client.query(`
      SELECT id, email FROM users WHERE email != '<EMAIL>' LIMIT 3
    `)
    
    for (const user of otherUsers.rows) {
      console.log(`\nCreating rankings for ${user.email}...`)
      
      // Delete existing rankings
      await client.query('DELETE FROM user_benefit_rankings WHERE user_id = $1', [user.id])
      
      // Create random rankings for some benefits
      const shuffledBenefits = benefits.rows.sort(() => Math.random() - 0.5).slice(0, 5)
      
      for (let i = 0; i < shuffledBenefits.length; i++) {
        const benefit = shuffledBenefits[i]
        const ranking = i + 1
        
        await client.query(`
          INSERT INTO user_benefit_rankings (user_id, benefit_id, ranking, created_at, updated_at)
          VALUES ($1, $2, $3, NOW() - INTERVAL '${Math.floor(Math.random() * 20)} days', NOW())
        `, [user.id, benefit.id, ranking])
        
        console.log(`  ${ranking}. ${benefit.name}`)
      }
    }
    
    // Verify the data
    const totalRankings = await client.query('SELECT COUNT(*) as count FROM user_benefit_rankings')
    console.log(`\n✅ Created ${totalRankings.rows[0].count} total rankings`)
    
    // Show summary by benefit
    const summary = await client.query(`
      SELECT 
        b.name,
        COUNT(ubr.id) as total_rankings,
        AVG(ubr.ranking::numeric) as avg_ranking
      FROM benefits b
      LEFT JOIN user_benefit_rankings ubr ON b.id = ubr.benefit_id
      GROUP BY b.id, b.name
      HAVING COUNT(ubr.id) > 0
      ORDER BY avg_ranking ASC
    `)
    
    console.log('\nRanking Summary:')
    summary.rows.forEach(row => {
      console.log(`  ${row.name}: ${row.total_rankings} rankings, avg ${parseFloat(row.avg_ranking).toFixed(1)}`)
    })
    
    client.release()
    await pool.end()
    
    console.log('\n🎉 Test data created successfully!')
    console.log('\nNow you can:')
    console.log('1. Sign <NAME_EMAIL>')
    console.log('2. Go to Analytics > Benefit Rankings tab')
    console.log('3. You should see the ranking analytics data')
    
  } catch (error) {
    console.error('Error:', error)
    await pool.end()
  }
}

createTestRankings()
