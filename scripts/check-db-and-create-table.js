const { Pool } = require('pg')

async function checkAndSetup() {
  // Try different connection configurations
  const configs = [
    {
      host: 'localhost',
      port: 5432,
      database: 'workwell',
      user: 'workwell_user',
      password: 'workwell_password',
    },
    {
      connectionString: 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'
    }
  ]

  for (const config of configs) {
    console.log('Trying connection config:', config.host ? `${config.user}@${config.host}:${config.port}/${config.database}` : 'connection string')
    
    const pool = new Pool(config)
    
    try {
      const client = await pool.connect()
      console.log('✓ Connected successfully!')
      
      // Check if user_benefit_rankings table exists
      const tableCheck = await client.query(`
        SELECT table_name FROM information_schema.tables 
        WHERE table_name = 'user_benefit_rankings'
      `)
      
      if (tableCheck.rows.length === 0) {
        console.log('Creating user_benefit_rankings table...')
        
        await client.query(`
          CREATE TABLE user_benefit_rankings (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
            ranking INTEGER NOT NULL CHECK (ranking >= 1 AND ranking <= 10),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(user_id, benefit_id)
          );
        `)
        
        // Create indexes
        await client.query('CREATE INDEX idx_user_benefit_rankings_user_id ON user_benefit_rankings(user_id);')
        await client.query('CREATE INDEX idx_user_benefit_rankings_benefit_id ON user_benefit_rankings(benefit_id);')
        await client.query('CREATE INDEX idx_user_benefit_rankings_ranking ON user_benefit_rankings(ranking);')
        await client.query('CREATE INDEX idx_user_benefit_rankings_updated_at ON user_benefit_rankings(updated_at);')
        
        console.log('✓ Table created successfully!')
      } else {
        console.log('✓ Table already exists')
      }
      
      // Check existing users
      const users = await client.query('SELECT id, email, payment_status, role FROM users LIMIT 5')
      console.log('\nExisting users:')
      users.rows.forEach(user => {
        console.log(`  ${user.email} (${user.payment_status}, ${user.role})`)
      })
      
      // Check benefits
      const benefits = await client.query('SELECT id, name FROM benefits LIMIT 5')
      console.log('\nExisting benefits:')
      benefits.rows.forEach(benefit => {
        console.log(`  ${benefit.name}`)
      })
      
      // Create test user if none exist
      if (users.rows.length === 0) {
        console.log('\nCreating test user...')
        await client.query(`
          INSERT INTO users (email, first_name, last_name, role, payment_status)
          VALUES ('<EMAIL>', 'Test', 'User', 'admin', 'paying')
        `)
        console.log('✓ Test user created')
      } else {
        // Update first user to be paying
        const firstUser = users.rows[0]
        await client.query(`
          UPDATE users SET payment_status = 'paying' WHERE id = $1
        `, [firstUser.id])
        console.log(`✓ Updated ${firstUser.email} to paying status`)
      }
      
      client.release()
      await pool.end()
      
      console.log('\n🎉 Database setup complete!')
      return true
      
    } catch (error) {
      console.error('Connection failed:', error.message)
      await pool.end()
    }
  }
  
  console.error('❌ All connection attempts failed')
  return false
}

checkAndSetup()
