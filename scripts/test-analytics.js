const { Pool } = require('pg')

async function testAnalytics() {
  const pool = new Pool({
    host: 'localhost',
    port: 5432,
    database: 'workwell',
    user: 'workwell_user',
    password: 'workwell_password',
  })

  try {
    const client = await pool.connect()
    console.log('🧪 Testing Benefit Ranking Analytics')
    console.log('=' .repeat(50))
    
    // Test 1: Check if table exists and has data
    console.log('\n1. Checking user_benefit_rankings table...')
    const tableCheck = await client.query(`
      SELECT COUNT(*) as count FROM user_benefit_rankings
    `)
    console.log(`   ✅ Table exists with ${tableCheck.rows[0].count} rankings`)
    
    // Test 2: Check users with rankings
    console.log('\n2. Checking users with rankings...')
    const usersWithRankings = await client.query(`
      SELECT 
        u.email,
        u.payment_status,
        COUNT(ubr.id) as ranking_count
      FROM users u
      LEFT JOIN user_benefit_rankings ubr ON u.id = ubr.user_id
      GROUP BY u.id, u.email, u.payment_status
      HAVING COUNT(ubr.id) > 0
      ORDER BY ranking_count DESC
    `)
    
    usersWithRankings.rows.forEach(user => {
      console.log(`   ${user.email} (${user.payment_status}): ${user.ranking_count} rankings`)
    })
    
    // Test 3: Check benefit ranking summary
    console.log('\n3. Benefit ranking summary...')
    const benefitSummary = await client.query(`
      SELECT 
        b.name,
        COUNT(ubr.id) as total_rankings,
        AVG(ubr.ranking::numeric) as avg_ranking,
        MIN(ubr.ranking) as best_ranking,
        MAX(ubr.ranking) as worst_ranking
      FROM benefits b
      LEFT JOIN user_benefit_rankings ubr ON b.id = ubr.benefit_id
      GROUP BY b.id, b.name
      HAVING COUNT(ubr.id) > 0
      ORDER BY avg_ranking ASC
      LIMIT 5
    `)
    
    benefitSummary.rows.forEach((benefit, index) => {
      console.log(`   ${index + 1}. ${benefit.name}`)
      console.log(`      Rankings: ${benefit.total_rankings}, Avg: ${parseFloat(benefit.avg_ranking).toFixed(2)}`)
    })
    
    // Test 4: Test API endpoint
    console.log('\n4. Testing API endpoint...')
    
    // Get admin session
    const adminSession = await client.query(`
      SELECT session_token FROM user_sessions us
      JOIN users u ON us.user_id = u.id
      WHERE u.email = '<EMAIL>'
      AND us.expires_at > NOW()
      ORDER BY us.created_at DESC
      LIMIT 1
    `)
    
    if (adminSession.rows.length > 0) {
      const sessionToken = adminSession.rows[0].session_token
      console.log(`   Using session: ${sessionToken.substring(0, 20)}...`)
      
      // Test API call
      const fetch = (await import('node-fetch')).default
      const response = await fetch('http://localhost:3001/api/analytics/benefit-rankings?period=30d&limit=5', {
        headers: {
          'Cookie': `session_token=${sessionToken}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        console.log(`   ✅ API Response: ${response.status}`)
        console.log(`   Summary: ${data.summary.totalRankings} rankings, ${data.summary.totalBenefitsRanked} benefits`)
        console.log(`   Most important: ${data.summary.mostImportantBenefit}`)
        console.log(`   Demo data: ${data.is_demo_data}`)
      } else {
        console.log(`   ❌ API Error: ${response.status} ${response.statusText}`)
        const errorText = await response.text()
        console.log(`   Error: ${errorText}`)
      }
    } else {
      console.log('   ❌ No valid admin session found')
    }
    
    client.release()
    await pool.end()
    
    console.log('\n🎉 Analytics test completed!')
    console.log('\nTo test in browser:')
    console.log('1. Go to http://localhost:3001/analytics')
    console.log('2. Click on "Benefit Rankings" tab')
    console.log('3. You should see real ranking data instead of "No ranking data available yet"')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
    await pool.end()
  }
}

testAnalytics()
