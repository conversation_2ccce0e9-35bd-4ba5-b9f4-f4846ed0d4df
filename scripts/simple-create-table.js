const { Pool } = require('pg')

async function createTable() {
  const pool = new Pool({
    host: 'localhost',
    port: 5432,
    database: 'workwell',
    user: 'workwell_user',
    password: 'workwell_password',
  })

  try {
    console.log('Connecting to database...')
    
    const client = await pool.connect()
    console.log('Connected successfully!')
    
    console.log('Creating user_benefit_rankings table...')
    
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS user_benefit_rankings (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        benefit_id UUID NOT NULL REFERENCES benefits(id) ON DELETE CASCADE,
        ranking INTEGER NOT NULL CHECK (ranking >= 1 AND ranking <= 10),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, benefit_id)
      );
    `
    
    await client.query(createTableSQL)
    console.log('Table created successfully!')
    
    // Create indexes
    console.log('Creating indexes...')
    await client.query('CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_user_id ON user_benefit_rankings(user_id);')
    await client.query('CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_benefit_id ON user_benefit_rankings(benefit_id);')
    await client.query('CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_ranking ON user_benefit_rankings(ranking);')
    await client.query('CREATE INDEX IF NOT EXISTS idx_user_benefit_rankings_updated_at ON user_benefit_rankings(updated_at);')
    
    console.log('Indexes created successfully!')
    
    // Verify
    const result = await client.query("SELECT table_name FROM information_schema.tables WHERE table_name = 'user_benefit_rankings'")
    console.log('Table exists:', result.rows.length > 0)
    
    client.release()
    
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await pool.end()
  }
}

createTable()
