const { Pool } = require('pg')
const fs = require('fs')
const path = require('path')

// Use environment variable directly
const DATABASE_URL = process.env.DATABASE_URL || 'postgresql://workwell_user:workwell_password@localhost:5432/workwell'

const pool = new Pool({
  connectionString: DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

async function runMigration() {
  const client = await pool.connect()
  
  try {
    console.log('Running user benefit rankings migration...')

    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', '006-add-user-benefit-rankings.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')

    // Execute the migration
    await client.query(migrationSQL)

    console.log('Migration completed successfully!')

    // Verify the table was created
    console.log('Verifying table creation...')
    const result = await client.query("SELECT table_name FROM information_schema.tables WHERE table_name = 'user_benefit_rankings'")

    if (result.rows.length > 0) {
      console.log('✓ user_benefit_rankings table created successfully')
    } else {
      console.log('✗ user_benefit_rankings table not found')
    }
    
  } catch (error) {
    console.error('Migration failed:', error.message)
    if (error.message.includes('already exists')) {
      console.log('Table already exists, checking structure...')
      try {
        const result = await client.query("SELECT column_name FROM information_schema.columns WHERE table_name = 'user_benefit_rankings'")
        console.log('Table columns:', result.rows.map(r => r.column_name))
      } catch (e) {
        console.error('Error checking table structure:', e.message)
      }
    } else {
      throw error
    }
  } finally {
    client.release()
    await pool.end()
  }
}

runMigration().catch(console.error)
