import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { getCurrentUser } from '@/lib/auth'
import type { UserBenefitRanking } from '@/types/database'

// GET /api/user/benefit-rankings - Get current user's benefit rankings
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const includeDetails = searchParams.get('includeDetails') === 'true'

    let sql = `
      SELECT 
        ubr.id,
        ubr.user_id,
        ubr.benefit_id,
        ubr.ranking,
        ubr.created_at,
        ubr.updated_at
    `

    if (includeDetails) {
      sql += `,
        b.name as benefit_name,
        b.category,
        b.icon,
        b.description,
        bc.display_name as category_display_name
      `
    }

    sql += `
      FROM user_benefit_rankings ubr
    `

    if (includeDetails) {
      sql += `
        LEFT JOIN benefits b ON ubr.benefit_id = b.id
        LEFT JOIN benefit_categories bc ON b.category_id = bc.id
      `
    }

    sql += `
      WHERE ubr.user_id = $1
      ORDER BY ubr.ranking ASC
    `

    const result = await query(sql, [user.id])

    const rankings = result.rows.map(row => {
      const ranking: UserBenefitRanking & { benefit?: any } = {
        id: row.id,
        user_id: row.user_id,
        benefit_id: row.benefit_id,
        ranking: row.ranking,
        created_at: row.created_at,
        updated_at: row.updated_at
      }

      if (includeDetails) {
        ranking.benefit = {
          id: row.benefit_id,
          name: row.benefit_name,
          category: row.category,
          icon: row.icon,
          description: row.description,
          category_display_name: row.category_display_name
        }
      }

      return ranking
    })

    return NextResponse.json({
      rankings,
      total: rankings.length
    })

  } catch (error) {
    console.error('Error fetching benefit rankings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch benefit rankings' },
      { status: 500 }
    )
  }
}

// POST /api/user/benefit-rankings - Create or update user's benefit rankings
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { rankings } = body

    if (!Array.isArray(rankings)) {
      return NextResponse.json(
        { error: 'Rankings must be an array' },
        { status: 400 }
      )
    }

    // Validate rankings
    for (const ranking of rankings) {
      if (!ranking.benefit_id || typeof ranking.ranking !== 'number') {
        return NextResponse.json(
          { error: 'Each ranking must have benefit_id and ranking number' },
          { status: 400 }
        )
      }

      if (ranking.ranking < 1 || ranking.ranking > 10) {
        return NextResponse.json(
          { error: 'Ranking must be between 1 and 10' },
          { status: 400 }
        )
      }
    }

    // Check for duplicate rankings
    const rankingValues = rankings.map(r => r.ranking)
    const uniqueRankings = new Set(rankingValues)
    if (rankingValues.length !== uniqueRankings.size) {
      return NextResponse.json(
        { error: 'Each ranking value must be unique' },
        { status: 400 }
      )
    }

    // Check for duplicate benefit_ids
    const benefitIds = rankings.map(r => r.benefit_id)
    const uniqueBenefitIds = new Set(benefitIds)
    if (benefitIds.length !== uniqueBenefitIds.size) {
      return NextResponse.json(
        { error: 'Each benefit can only be ranked once' },
        { status: 400 }
      )
    }

    // Verify all benefits exist
    const benefitCheckSql = `
      SELECT id FROM benefits WHERE id = ANY($1)
    `
    const benefitCheckResult = await query(benefitCheckSql, [benefitIds])
    
    if (benefitCheckResult.rows.length !== benefitIds.length) {
      return NextResponse.json(
        { error: 'One or more benefits do not exist' },
        { status: 400 }
      )
    }

    // Begin transaction to update rankings
    await query('BEGIN')

    try {
      // Delete existing rankings for this user
      await query(
        'DELETE FROM user_benefit_rankings WHERE user_id = $1',
        [user.id]
      )

      // Insert new rankings
      const insertPromises = rankings.map(ranking => 
        query(
          `INSERT INTO user_benefit_rankings (user_id, benefit_id, ranking)
           VALUES ($1, $2, $3)`,
          [user.id, ranking.benefit_id, ranking.ranking]
        )
      )

      await Promise.all(insertPromises)
      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'Benefit rankings updated successfully',
        count: rankings.length
      })

    } catch (error) {
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('Error updating benefit rankings:', error)
    return NextResponse.json(
      { error: 'Failed to update benefit rankings' },
      { status: 500 }
    )
  }
}

// DELETE /api/user/benefit-rankings - Delete all user's benefit rankings
export async function DELETE(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const result = await query(
      'DELETE FROM user_benefit_rankings WHERE user_id = $1',
      [user.id]
    )

    return NextResponse.json({
      success: true,
      message: 'All benefit rankings deleted successfully',
      deletedCount: result.rowCount
    })

  } catch (error) {
    console.error('Error deleting benefit rankings:', error)
    return NextResponse.json(
      { error: 'Failed to delete benefit rankings' },
      { status: 500 }
    )
  }
}
