import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { getAnalyticsAccessInfo } from '@/lib/analytics-access-control'
import { generateDemoSearchTrends } from '@/lib/demo-analytics-generator'

// For demo purposes, we'll simulate search trends
// In production, you'd track actual search queries in a separate table

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '7d' // 7d, 30d, 90d
    const limit = parseInt(searchParams.get('limit') || '10')

    // Check analytics access level
    const accessInfo = await getAnalyticsAccessInfo()

    if (accessInfo.level === 'none') {
      return NextResponse.json(
        { error: 'Authentication required to access analytics' },
        { status: 401 }
      )
    }

    // If user is on demo mode, return demo data
    if (accessInfo.isDemoMode) {
      const demoData = generateDemoSearchTrends(period, limit)
      return NextResponse.json(demoData)
    }

    // Get real search trends based on benefit popularity and activity
    const trendsQuery = `
      SELECT
        b.name as search_term,
        COUNT(cb.id) as company_count,
        b.category,
        b.icon,
        -- Calculate trend score based on real metrics
        (COUNT(cb.id) * 10 +
         COUNT(CASE WHEN cb.is_verified THEN 1 END) * 25 +
         (SELECT COUNT(*) FROM benefit_verifications bv
          JOIN company_benefits cb2 ON bv.company_benefit_id = cb2.id
          WHERE cb2.benefit_id = b.id) * 5) as trend_score,
        -- Count total verifications for this benefit
        (SELECT COUNT(*) FROM benefit_verifications bv
         JOIN company_benefits cb2 ON bv.company_benefit_id = cb2.id
         WHERE cb2.benefit_id = b.id) as total_verifications,
        -- Count verified companies offering this benefit
        COUNT(CASE WHEN cb.is_verified THEN 1 END) as verified_companies
      FROM benefits b
      LEFT JOIN company_benefits cb ON b.id = cb.benefit_id
      GROUP BY b.id, b.name, b.category, b.icon
      HAVING COUNT(cb.id) > 0
      ORDER BY trend_score DESC, company_count DESC
      LIMIT $1
    `

    const result = await query(trendsQuery, [limit])

    // Calculate realistic search data based on actual benefit popularity
    const trends = result.rows.map((row, index) => {
      const baseSearches = Math.max(10, row.company_count * 15 + row.total_verifications * 3)
      const currentPeriodSearches = Math.floor(baseSearches * (0.8 + Math.random() * 0.4))
      const previousPeriodSearches = Math.floor(baseSearches * (0.7 + Math.random() * 0.4))
      const change = Math.round(((currentPeriodSearches - previousPeriodSearches) / previousPeriodSearches) * 100)

      return {
        ...row,
        rank: index + 1,
        search_count: currentPeriodSearches,
        change: Math.max(-50, Math.min(50, change)), // Cap change at ±50%
        period_searches: currentPeriodSearches,
        previous_period_searches: previousPeriodSearches
      }
    })

    return NextResponse.json({
      period,
      trends,
      total_searches: trends.reduce((sum, trend) => sum + trend.period_searches, 0),
      generated_at: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error fetching search trends:', error)
    return NextResponse.json(
      { error: 'Failed to fetch search trends' },
      { status: 500 }
    )
  }
}
